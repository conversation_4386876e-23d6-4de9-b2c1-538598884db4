package repository

import (
	"archive/zip"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"
	"adgitops-ui/src/backend/services"

	"github.com/ktrysmt/go-bitbucket"
)

// BitbucketRepository handles the Bitbucket integration and repository monitoring
type BitbucketRepository struct {
	config         models.RepositoryConfig
	localRepoDir   string
	lastCommit     string
	isPolling      bool
	syncInProgress bool       // Flag to track manual sync operations
	syncMutex      sync.Mutex // Mutex to protect syncInProgress flag
	callbacks      []func()
	httpClient     *http.Client
	syncLogger     *services.SyncLogger
	repoID         string
	bbClient       *bitbucket.Client
}

// NewBitbucketRepository creates a new Bitbucket repository instance
func NewBitbucketRepository(config models.RepositoryConfig, localRepoDir string, syncLogger *services.SyncLogger) *BitbucketRepository {
	return &BitbucketRepository{
		config:       config,
		localRepoDir: localRepoDir,
		callbacks:    make([]func(), 0),
		httpClient:   &http.Client{Timeout: 30 * time.Second},
		syncLogger:   syncLogger,
		repoID:       config.ID,
	}
}

// InitClient initializes the Bitbucket client
func (br *BitbucketRepository) InitClient() error {
	if br.config.URL == "" {
		return fmt.Errorf("Bitbucket URL is required")
	}

	// Validate that either token or username/password is provided
	if br.config.Token == "" && (br.config.BitbucketUsername == "" || br.config.BitbucketPassword == "") {
		return fmt.Errorf("either token or username/password is required for Bitbucket authentication")
	}

	// Create a custom transport with TLS verification disabled and longer timeouts
	customTransport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, // Skip TLS certificate verification
		},
		TLSHandshakeTimeout: 60 * time.Second, // Increase TLS handshake timeout
		DisableKeepAlives:   false,
		MaxIdleConns:        10,
		IdleConnTimeout:     60 * time.Second,
	}

	// Create HTTP client with longer timeout for API calls
	br.httpClient = &http.Client{
		Timeout:   120 * time.Second, // Increase overall timeout
		Transport: customTransport,
	}

	// Initialize Bitbucket API client with appropriate authentication
	if br.config.Token != "" {
		// Use token-based authentication
		br.bbClient = bitbucket.NewOAuthbearerToken(br.config.Token)
	} else {
		// Use basic authentication
		br.bbClient = bitbucket.NewBasicAuth(
			br.config.BitbucketUsername,
			br.config.BitbucketPassword,
		)
	}

	// Set the custom endpoint if using Bitbucket Server (self-hosted)
	if br.config.URL != "https://api.bitbucket.org" && br.config.URL != "https://bitbucket.org" {
		baseURL, err := url.Parse(br.config.URL)
		if err != nil {
			return fmt.Errorf("invalid Bitbucket URL: %w", err)
		}
		br.bbClient.SetApiBaseURL(*baseURL)
	}

	return nil
}

// StartPolling begins periodic polling for repository changes
func (br *BitbucketRepository) StartPolling() error {
	if br.isPolling {
		return nil // Already polling
	}

	// Create a local directory for the repository if it doesn't exist
	if err := br.EnsureLocalRepoExists(); err != nil {
		return err
	}

	br.isPolling = true
	go br.pollRepository()
	return nil
}

// StopPolling stops the periodic polling
func (br *BitbucketRepository) StopPolling() {
	br.isPolling = false
}

// RegisterChangeCallback registers a function to be called when repository changes are detected
func (br *BitbucketRepository) RegisterChangeCallback(callback func()) {
	br.callbacks = append(br.callbacks, callback)
}

// pollRepository periodically checks for changes in the repository
func (br *BitbucketRepository) pollRepository() {
	ticker := time.NewTicker(time.Duration(br.config.PollFrequency) * time.Second)
	defer ticker.Stop()

	for {
		if !br.isPolling {
			return
		}

		// Log polling attempt
		br.syncLogger.AddLog(br.repoID, "Checking for repository changes", models.LogLevelInfo)

		hasChanges, err := br.checkForChanges()
		if err != nil {
			br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Error checking for changes: %v", err), models.LogLevelError)
		} else if hasChanges {
			br.syncLogger.AddLog(br.repoID, "Repository changes detected, triggering callbacks", models.LogLevelInfo)

			// Call all registered callbacks
			for _, callback := range br.callbacks {
				callback()
			}
		}

		select {
		case <-ticker.C:
			// Continue polling
		}
	}
}

// SyncRepository downloads the latest repository files using the Bitbucket API
func (br *BitbucketRepository) SyncRepository() error {
	// Use mutex to safely check and set syncInProgress flag
	br.syncMutex.Lock()
	if br.syncInProgress {
		br.syncMutex.Unlock()
		br.syncLogger.AddLog(br.repoID, "Repository is already being synchronized, please wait for the current operation to complete", models.LogLevelWarning)
		return fmt.Errorf("repository is already being synchronized")
	}

	// Set syncInProgress flag to true
	br.syncInProgress = true
	br.syncMutex.Unlock()

	// Make sure to reset the flag when we're done
	defer func() {
		br.syncMutex.Lock()
		br.syncInProgress = false
		br.syncMutex.Unlock()
	}()

	// Ensure local repository directory exists
	if err := br.EnsureLocalRepoExists(); err != nil {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Failed to create local repository directory: %v", err), models.LogLevelError)
		return err
	}

	// Get latest commit hash
	br.syncLogger.AddLog(br.repoID, "Checking for latest commit...", models.LogLevelInfo)
	latestCommitHash, err := br.getLatestCommitHash()
	if err != nil {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Failed to get latest commit hash: %v", err), models.LogLevelError)
		return fmt.Errorf("failed to get latest commit hash: %w", err)
	}

	// If already at latest commit, no need to sync
	if br.lastCommit == latestCommitHash && br.lastCommit != "" {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Repository already at latest commit: %s", latestCommitHash), models.LogLevelSuccess)
		// Update the last sync time
		currentTime := time.Now().Format(time.RFC3339)

		// Get the correct repository path
		repoPath := br.GetLocalRepoPath()
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Updating sync time at path: %s", repoPath), models.LogLevelInfo)

		// Save the last sync time to a file
		syncTimeFile := filepath.Join(repoPath, "LAST_SYNC")
		if err := os.WriteFile(syncTimeFile, []byte(currentTime), 0644); err != nil {
			br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Warning: Failed to save sync time: %v", err), models.LogLevelWarning)
		} else {
			br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Sync time saved to: %s", syncTimeFile), models.LogLevelInfo)
		}
		return nil
	}

	// Download repository contents
	br.syncLogger.AddLog(br.repoID, "Fetching repository files...", models.LogLevelInfo)
	if err := br.downloadRepositoryContents(latestCommitHash); err != nil {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Failed to download repository contents: %v", err), models.LogLevelError)
		return fmt.Errorf("failed to download repository contents: %w", err)
	}

	// Update last commit hash
	br.lastCommit = latestCommitHash
	br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Synced repository to commit %s", br.lastCommit), models.LogLevelSuccess)

	// Get the correct repository path
	repoPath := br.GetLocalRepoPath()
	br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Saving metadata to path: %s", repoPath), models.LogLevelInfo)

	// Save the commit hash to a file for persistence
	commitFile := filepath.Join(repoPath, "COMMIT_HASH")
	if err := os.WriteFile(commitFile, []byte(latestCommitHash), 0644); err != nil {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Warning: Failed to save commit hash: %v", err), models.LogLevelWarning)
	} else {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Commit hash saved to: %s", commitFile), models.LogLevelInfo)
	}

	// Save the last sync time to a file
	currentTime := time.Now().Format(time.RFC3339)
	syncTimeFile := filepath.Join(repoPath, "LAST_SYNC")
	if err := os.WriteFile(syncTimeFile, []byte(currentTime), 0644); err != nil {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Warning: Failed to save sync time: %v", err), models.LogLevelWarning)
	} else {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Sync time saved to: %s", syncTimeFile), models.LogLevelInfo)
	}

	return nil
}

// getLatestCommitHash retrieves the latest commit hash from Bitbucket
func (br *BitbucketRepository) getLatestCommitHash() (string, error) {
	// For Bitbucket API, we need the workspace and repository slug
	owner := br.config.BitbucketWorkspace
	repoSlug := br.config.BitbucketRepoSlug

	// Log the request parameters for debugging
	br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Requesting commits for Bitbucket repository: Workspace=%s, RepoSlug=%s",
		owner, repoSlug), models.LogLevelInfo)

	// Add more debugging information about API details
	apiBaseURL := br.bbClient.GetApiBaseURL()
	br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Bitbucket API Base URL: %s", apiBaseURL), models.LogLevelInfo)

	// Log authentication method for debugging (without exposing credentials)
	if br.config.Token != "" {
		br.syncLogger.AddLog(br.repoID, "Authentication method: Bearer Token", models.LogLevelInfo)
		// Log token length and prefix for debugging
		tokenLen := len(br.config.Token)
		tokenPrefix := ""
		if tokenLen > 4 {
			tokenPrefix = br.config.Token[0:4]
		}
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Token info: Length=%d, Prefix=%s...", tokenLen, tokenPrefix), models.LogLevelInfo)
	} else if br.config.BitbucketUsername != "" {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Authentication method: Basic Auth (Username: %s)", br.config.BitbucketUsername), models.LogLevelInfo)
	} else {
		br.syncLogger.AddLog(br.repoID, "No authentication information found!", models.LogLevelWarning)
	}

	// Always use Bitbucket Server endpoint format for self-hosted instances
	endpoint := fmt.Sprintf("%s/rest/api/1.0/projects/%s/repos/%s/commits", apiBaseURL, owner, repoSlug)
	br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Full commits API endpoint: %s", endpoint), models.LogLevelInfo)

	// Check if required parameters are set
	if owner == "" {
		errMsg := "Bitbucket Workspace is empty in repository configuration"
		br.syncLogger.AddLog(br.repoID, errMsg, models.LogLevelError)
		return "", fmt.Errorf(errMsg)
	}

	if repoSlug == "" {
		errMsg := "Bitbucket Repository Slug is empty in repository configuration"
		br.syncLogger.AddLog(br.repoID, errMsg, models.LogLevelError)
		return "", fmt.Errorf(errMsg)
	}

	// Set pagination limit to only get the most recent commit
	br.syncLogger.AddLog(br.repoID, "Set API pagination limit to 1 (newest commit only)", models.LogLevelInfo)

	// Create a custom request to use our HTTP client with TLS verification disabled
	br.syncLogger.AddLog(br.repoID, "Making API request to get latest commit...", models.LogLevelInfo)

	// Create a new HTTP request with proper authentication headers
	req, err := http.NewRequest("GET", endpoint+"?limit=1", nil)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to create HTTP request: %v", err)
		br.syncLogger.AddLog(br.repoID, errMsg, models.LogLevelError)
		return "", fmt.Errorf(errMsg)
	}

	// Add authentication headers
	if br.config.Token != "" {
		req.Header.Add("Authorization", "Bearer "+br.config.Token)
	} else if br.config.BitbucketUsername != "" && br.config.BitbucketPassword != "" {
		req.SetBasicAuth(br.config.BitbucketUsername, br.config.BitbucketPassword)
	}

	// Add content type header
	req.Header.Add("Content-Type", "application/json")

	// Make the request with our client that has TLS verification disabled
	resp, err := br.httpClient.Do(req)
	if err != nil {
		errMsg := fmt.Sprintf("API request failed with error: %v", err)
		br.syncLogger.AddLog(br.repoID, errMsg, models.LogLevelError)
		return "", fmt.Errorf("failed to get commits: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		errMsg := fmt.Sprintf("API request failed with status: %d %s", resp.StatusCode, resp.Status)
		br.syncLogger.AddLog(br.repoID, errMsg, models.LogLevelError)

		if resp.StatusCode == http.StatusNotFound {
			br.syncLogger.AddLog(
				br.repoID,
				fmt.Sprintf("Repository not found (404): Workspace=%s, RepoSlug=%s. Please verify these values",
					owner, repoSlug),
				models.LogLevelError,
			)

			// Add troubleshooting tips
			br.syncLogger.AddLog(br.repoID, "Bitbucket 404 troubleshooting tips:", models.LogLevelInfo)
			br.syncLogger.AddLog(br.repoID, "1. Verify the workspace and repository slug are correct", models.LogLevelInfo)
			br.syncLogger.AddLog(br.repoID, "2. Check if you can access the repository in the browser", models.LogLevelInfo)
			br.syncLogger.AddLog(br.repoID, "3. Ensure your authentication token has access to this repository", models.LogLevelInfo)

			// Suggest a URL to check in the browser
			browserURL := fmt.Sprintf("%s/projects/%s/repos/%s",
				strings.TrimSuffix(apiBaseURL, "/rest/api/1.0"), owner, repoSlug)
			br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Try accessing the repository in browser: %s", browserURL), models.LogLevelInfo)

			return "", fmt.Errorf("Repository not found (404): Workspace=%s, RepoSlug=%s. Please verify these values", owner, repoSlug)
		}

		return "", fmt.Errorf("API request failed with status %d", resp.StatusCode)
	}

	// Read and parse the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to read API response: %v", err)
		br.syncLogger.AddLog(br.repoID, errMsg, models.LogLevelError)
		return "", fmt.Errorf("failed to read API response: %w", err)
	}

	// Parse the JSON response
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		errMsg := fmt.Sprintf("Failed to parse API response: %v", err)
		br.syncLogger.AddLog(br.repoID, errMsg, models.LogLevelError)
		return "", fmt.Errorf("failed to parse API response: %w", err)
	}

	// Extract the commit hash based on the Bitbucket Server API response format
	values, ok := result["values"].([]interface{})
	if !ok || len(values) == 0 {
		errMsg := "No commits found in repository"
		br.syncLogger.AddLog(br.repoID, errMsg, models.LogLevelError)
		return "", fmt.Errorf(errMsg)
	}

	// Get the first commit (most recent)
	commit, ok := values[0].(map[string]interface{})
	if !ok {
		errMsg := "Invalid commit data format"
		br.syncLogger.AddLog(br.repoID, errMsg, models.LogLevelError)
		return "", fmt.Errorf(errMsg)
	}

	// Extract the commit ID (hash)
	commitID, ok := commit["id"].(string)
	if !ok || commitID == "" {
		errMsg := "Commit ID not found or invalid"
		br.syncLogger.AddLog(br.repoID, errMsg, models.LogLevelError)
		return "", fmt.Errorf(errMsg)
	}

	br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Found latest commit: %s", commitID), models.LogLevelSuccess)
	return commitID, nil
}

// downloadRepositoryContents downloads the repository contents using the Bitbucket API
func (br *BitbucketRepository) downloadRepositoryContents(commitHash string) error {
	// Path to download the repository contents
	localPath := br.GetLocalRepoPath()

	// Download repository archive using direct HTTP request
	// The library doesn't provide direct download functionality
	owner := br.config.BitbucketWorkspace
	repoSlug := br.config.BitbucketRepoSlug

	var downloadURL string
	if strings.Contains(br.config.URL, "bitbucket.org") {
		// Bitbucket Cloud
		downloadURL = fmt.Sprintf("https://bitbucket.org/%s/%s/get/%s.zip",
			owner, repoSlug, commitHash)
	} else {
		// Bitbucket Server
		downloadURL = fmt.Sprintf("%s/rest/api/1.0/projects/%s/repos/%s/archive?at=%s&format=zip",
			br.config.URL, owner, repoSlug, commitHash)
	}

	// Create a new request
	req, err := http.NewRequest("GET", downloadURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create download request: %w", err)
	}

	// Add authentication
	if br.config.Token != "" {
		req.Header.Add("Authorization", "Bearer "+br.config.Token)
	} else {
		req.SetBasicAuth(br.config.BitbucketUsername, br.config.BitbucketPassword)
	}

	// Execute the request
	resp, err := br.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to download repository archive: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Create temporary file for the zip archive
	tempFile, err := os.CreateTemp("", "bitbucket-repo-*.zip")
	if err != nil {
		return fmt.Errorf("failed to create temporary file: %w", err)
	}
	defer os.Remove(tempFile.Name())

	// Save response to temporary file
	_, err = io.Copy(tempFile, resp.Body)
	if err != nil {
		return fmt.Errorf("failed to save archive: %w", err)
	}
	tempFile.Close()

	// Clear the repository directory (except COMMIT_HASH file)
	entries, err := os.ReadDir(localPath)
	if err != nil {
		// If directory doesn't exist, create it
		if os.IsNotExist(err) {
			if err := os.MkdirAll(localPath, 0755); err != nil {
				return fmt.Errorf("failed to create directory: %w", err)
			}
		} else {
			return fmt.Errorf("failed to read directory: %w", err)
		}
	} else {
		// Clear existing directory contents
		for _, entry := range entries {
			if entry.Name() != "COMMIT_HASH" && entry.Name() != "LAST_SYNC" {
				entryPath := filepath.Join(localPath, entry.Name())
				if err := os.RemoveAll(entryPath); err != nil {
					return fmt.Errorf("failed to remove %s: %w", entryPath, err)
				}
			}
		}
	}

	// Extract zip archive
	br.syncLogger.AddLog(br.repoID, "Extracting repository files...", models.LogLevelInfo)
	if err := br.extractZip(tempFile.Name(), localPath); err != nil {
		return fmt.Errorf("failed to extract archive: %w", err)
	}

	// Write the current commit hash to a file
	commitFile := filepath.Join(localPath, "COMMIT_HASH")
	if err := os.WriteFile(commitFile, []byte(commitHash), 0644); err != nil {
		return fmt.Errorf("failed to write commit hash: %w", err)
	}

	// Verify the repository path exists
	if _, err := os.Stat(localPath); os.IsNotExist(err) {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Repository path doesn't exist: %s", localPath), models.LogLevelError)
		if err := os.MkdirAll(localPath, 0755); err != nil {
			br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Failed to create repository path: %v", err), models.LogLevelError)
			return fmt.Errorf("failed to create repository path: %w", err)
		}
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Repository path created: %s", localPath), models.LogLevelInfo)
	}

	// Test file write to debug permissions issues
	testFile := filepath.Join(localPath, "TEST_WRITE")
	if err := os.WriteFile(testFile, []byte("test"), 0644); err != nil {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("TEST WRITE FAILED: %v", err), models.LogLevelError)
	} else {
		br.syncLogger.AddLog(br.repoID, "Test write succeeded", models.LogLevelInfo)
		// Clean up test file
		os.Remove(testFile)
	}

	// Write the last sync time to a file with detailed logging
	currentTime := time.Now().Format(time.RFC3339)
	syncTimeFile := filepath.Join(localPath, "LAST_SYNC")
	br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Writing LAST_SYNC to: %s with time: %s", syncTimeFile, currentTime), models.LogLevelInfo)

	if err := os.WriteFile(syncTimeFile, []byte(currentTime), 0644); err != nil {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("ERROR writing LAST_SYNC: %v", err), models.LogLevelError)
		return fmt.Errorf("failed to write last sync time: %w", err)
	}

	// Verify the file was created
	if _, err := os.Stat(syncTimeFile); os.IsNotExist(err) {
		br.syncLogger.AddLog(br.repoID, "LAST_SYNC file was not created even though no error was returned", models.LogLevelError)
	} else {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("LAST_SYNC file created successfully at: %s", syncTimeFile), models.LogLevelSuccess)
	}

	return nil
}

// checkForChanges checks if the remote repository has changes
func (br *BitbucketRepository) checkForChanges() (bool, error) {
	// Ensure local repo exists
	if err := br.EnsureLocalRepoExists(); err != nil {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Failed to ensure local repository exists: %v", err), models.LogLevelError)
		return false, fmt.Errorf("failed to ensure local repository exists: %w", err)
	}

	// Log the poll frequency for debugging
	br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Repository poll frequency: %d seconds", br.config.PollFrequency), models.LogLevelInfo)

	// Get the latest commit from Bitbucket
	latestCommit, err := br.getLatestCommitHash()
	if err != nil {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Failed to get latest commit hash: %v", err), models.LogLevelError)
		return false, fmt.Errorf("failed to get latest commit hash: %w", err)
	}

	// Read the current commit hash from file
	commitFile := filepath.Join(br.GetLocalRepoPath(), "COMMIT_HASH")
	currentCommit := ""

	// Try to read existing commit hash file if it exists
	if data, err := os.ReadFile(commitFile); err == nil {
		currentCommit = string(data)
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Current commit hash from file: %s", currentCommit), models.LogLevelInfo)
	} else {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Failed to read current commit hash file: %v", err), models.LogLevelWarning)
	}

	// Update in-memory last commit if it's empty
	if br.lastCommit == "" && currentCommit != "" {
		br.lastCommit = currentCommit
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Updated in-memory last commit to: %s", br.lastCommit), models.LogLevelInfo)
	}

	// If we don't have a previous commit, or the commits are different, there are changes
	hasChanges := currentCommit != latestCommit || currentCommit == ""

	// Log detailed comparison
	br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Commit comparison - Local: %s, Remote: %s", currentCommit, latestCommit), models.LogLevelInfo)

	if hasChanges {
		br.syncLogger.AddLog(br.repoID, "Repository has changes available", models.LogLevelInfo)

		// Log the number of registered callbacks
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Number of registered callbacks: %d", len(br.callbacks)), models.LogLevelInfo)

		// Check if we have any callbacks registered
		if len(br.callbacks) == 0 {
			br.syncLogger.AddLog(br.repoID, "WARNING: No callbacks registered for repository changes", models.LogLevelWarning)
		}
	} else {
		br.syncLogger.AddLog(br.repoID, "Repository is up to date", models.LogLevelInfo)
	}

	return hasChanges, nil
}

// extractZip extracts a zip archive to the specified destination
func (br *BitbucketRepository) extractZip(zipFile, destination string) error {
	reader, err := zip.OpenReader(zipFile)
	if err != nil {
		return fmt.Errorf("failed to open zip file: %w", err)
	}
	defer reader.Close()

	// Detect if archive has a wrapper directory by checking if all files start with the same prefix
	var commonPrefix string
	hasWrapperDir := true

	if len(reader.File) > 0 {
		// Get the first path component of the first file
		firstFile := reader.File[0].Name
		if idx := strings.Index(firstFile, "/"); idx != -1 {
			commonPrefix = firstFile[:idx+1]

			// Check if all files start with this prefix
			for _, file := range reader.File {
				if !strings.HasPrefix(file.Name, commonPrefix) {
					hasWrapperDir = false
					break
				}
			}
		} else {
			hasWrapperDir = false
		}
	}

	// Log the detected structure for debugging
	if hasWrapperDir {
		br.syncLogger.AddLog(br.repoID, fmt.Sprintf("Detected wrapper directory with prefix: %s", commonPrefix), models.LogLevelInfo)
	} else {
		br.syncLogger.AddLog(br.repoID, "No wrapper directory detected in archive", models.LogLevelInfo)
	}

	// Create any subdirectories if they don't exist
	for _, file := range reader.File {
		var destPath string

		if hasWrapperDir && commonPrefix != "" {
			// Remove the wrapper directory prefix to preserve exact repository structure
			relativePath := strings.TrimPrefix(file.Name, commonPrefix)
			if relativePath == "" {
				continue // Skip the wrapper directory itself
			}
			destPath = filepath.Join(destination, relativePath)
		} else {
			// No wrapper directory, preserve the full path
			destPath = filepath.Join(destination, file.Name)
		}

		if file.FileInfo().IsDir() {
			if err := os.MkdirAll(destPath, 0755); err != nil {
				return fmt.Errorf("failed to create directory %s: %w", destPath, err)
			}
			continue
		}

		// Make sure the directory exists
		if err := os.MkdirAll(filepath.Dir(destPath), 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %w", filepath.Dir(destPath), err)
		}

		// Extract file
		srcFile, err := file.Open()
		if err != nil {
			return fmt.Errorf("failed to open file in zip: %w", err)
		}

		destFile, err := os.Create(destPath)
		if err != nil {
			srcFile.Close()
			return fmt.Errorf("failed to create destination file: %w", err)
		}

		_, err = io.Copy(destFile, srcFile)
		srcFile.Close()
		destFile.Close()

		if err != nil {
			return fmt.Errorf("failed to copy file contents: %w", err)
		}
	}

	return nil
}

// EnsureLocalRepoExists makes sure the local repository directory exists
func (br *BitbucketRepository) EnsureLocalRepoExists() error {
	repoPath := br.GetLocalRepoPath()
	if _, err := os.Stat(repoPath); os.IsNotExist(err) {
		if err := os.MkdirAll(repoPath, 0755); err != nil {
			return fmt.Errorf("failed to create repository directory: %w", err)
		}
	}
	return nil
}

// GetLocalRepoPath returns the path to the local repository
func (br *BitbucketRepository) GetLocalRepoPath() string {
	// Just return the localRepoDir directly, which already includes the config.ID
	return br.localRepoDir
}

// GetRepositoryStatus returns the current status of the repository
func (br *BitbucketRepository) GetRepositoryStatus() models.RepositoryStatus {
	// Safely check syncInProgress flag
	br.syncMutex.Lock()
	syncInProgress := br.syncInProgress
	br.syncMutex.Unlock()

	status := models.RepositoryStatus{
		LastSync:       "",
		LastCommit:     br.lastCommit,
		HasChanges:     false,          // Will be determined during next poll
		SyncInProgress: syncInProgress, // Use the actual sync status
	}

	// Get the correct repository path
	repoPath := br.GetLocalRepoPath()

	// Use in-memory commit hash if available, otherwise read from file
	if status.LastCommit == "" {
		commitFile := filepath.Join(repoPath, "COMMIT_HASH")
		if data, err := os.ReadFile(commitFile); err == nil {
			status.LastCommit = string(data)
		}
	}

	// Read last sync time from file
	syncTimeFile := filepath.Join(repoPath, "LAST_SYNC")

	if _, err := os.Stat(syncTimeFile); os.IsNotExist(err) {
		// Try creating the file with current time as fallback if it doesn't exist
		currentTime := time.Now().Format(time.RFC3339)
		if err := os.WriteFile(syncTimeFile, []byte(currentTime), 0644); err == nil {
			status.LastSync = currentTime
		}
	} else {
		if data, err := os.ReadFile(syncTimeFile); err == nil {
			status.LastSync = string(data)
		}
	}

	return status
}

// UpdateConfig updates the Bitbucket configuration
func (br *BitbucketRepository) UpdateConfig(config models.RepositoryConfig) error {
	// Save old values
	oldPolling := br.isPolling
	oldConfig := br.config

	// Update config
	br.config = config
	br.repoID = config.ID

	// Re-initialize client if needed
	if oldConfig.URL != config.URL ||
		oldConfig.Token != config.Token ||
		oldConfig.BitbucketUsername != config.BitbucketUsername ||
		oldConfig.BitbucketPassword != config.BitbucketPassword {
		if err := br.InitClient(); err != nil {
			// Restore old config on error
			br.config = oldConfig
			br.repoID = oldConfig.ID
			return err
		}
	}

	// Handle polling changes
	if oldPolling && !br.isPolling && config.IsActive {
		if err := br.StartPolling(); err != nil {
			return err
		}
	} else if !config.IsActive && br.isPolling {
		br.StopPolling()
	}

	return nil
}

// GetConfig returns the current repository configuration
func (br *BitbucketRepository) GetConfig() models.RepositoryConfig {
	return br.config
}

// GetID returns the repository ID
func (br *BitbucketRepository) GetID() string {
	return br.repoID
}
