package repository

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"adgitops-ui/src/backend/models"
	"adgitops-ui/src/backend/services"
)

// SearchController interface for triggering reindexing
type SearchController interface {
	ReindexRepository(repoId string) error
}

// RepositoryManager handles multiple repository instances
type RepositoryManager struct {
	repositories     map[string]Repository
	configsPath      string
	syncLogger       *services.SyncLogger
	projectRoot      string
	searchController SearchController
}

// NewRepositoryManager creates a new repository manager instance
func NewRepositoryManager(configsPath string, syncLogger *services.SyncLogger) *RepositoryManager {
	// Find the project root directory (where the repos folder exists)
	projectRoot := getProjectRoot()
	log.Printf("Project root directory: %s", projectRoot)

	rm := &RepositoryManager{
		configsPath:  configsPath,
		repositories: make(map[string]Repository),
		syncLogger:   syncLogger,
		projectRoot:  projectRoot,
	}

	// Load initial configurations
	if err := rm.LoadConfigurations(); err != nil {
		log.Printf("Failed to load initial configurations: %v", err)
	}

	return rm
}

// getProjectRoot tries to find the project root directory
func getProjectRoot() string {
	// Start with the current working directory
	wd, err := os.Getwd()
	if err != nil {
		log.Printf("Error getting current working directory: %v, using . as fallback", err)
		return "."
	}

	// If we're in the src/backend directory, go up two levels
	if strings.HasSuffix(wd, filepath.Join("src", "backend")) {
		return filepath.Dir(filepath.Dir(wd))
	}

	// If we're in the src directory, go up one level
	if strings.HasSuffix(wd, "src") {
		return filepath.Dir(wd)
	}

	// Check if we're already at the project root (contains repos directory)
	if _, err := os.Stat(filepath.Join(wd, "repos")); err == nil {
		return wd
	}

	// If we can't determine, just use the current directory
	return wd
}

// SetSearchController sets the search controller for triggering reindexing
func (rm *RepositoryManager) SetSearchController(searchController SearchController) {
	rm.searchController = searchController
}

// LoadConfigurations loads repository configurations from file
func (rm *RepositoryManager) LoadConfigurations() error {
	log.Printf("Loading configurations from: %s", rm.configsPath)

	// Ensure configurations directory exists
	configDir := filepath.Dir(rm.configsPath)
	log.Printf("Configuration directory: %s", configDir)

	if _, err := os.Stat(configDir); os.IsNotExist(err) {
		log.Printf("Configuration directory doesn't exist, creating: %s", configDir)
		if err := os.MkdirAll(configDir, 0755); err != nil {
			log.Printf("Failed to create configurations directory: %v", err)
			return fmt.Errorf("failed to create configurations directory: %w", err)
		}
	}

	// Check if configuration file exists
	_, err := os.Stat(rm.configsPath)
	if os.IsNotExist(err) {
		log.Printf("Configuration file doesn't exist, creating empty file: %s", rm.configsPath)
		// Create an empty configuration file
		emptyConfigs := models.RepositoryConfigList{
			Configs: []models.RepositoryConfig{},
		}
		return rm.SaveConfigurations(emptyConfigs)
	} else if err != nil {
		log.Printf("Error checking configuration file: %v", err)
		return fmt.Errorf("error checking configuration file: %w", err)
	}

	// Read configurations from file
	log.Printf("Reading configurations from file: %s", rm.configsPath)
	configsData, err := os.ReadFile(rm.configsPath)
	if err != nil {
		log.Printf("Failed to read configurations file: %v", err)
		return fmt.Errorf("failed to read configurations file: %w", err)
	}

	log.Printf("Successfully read %d bytes from configuration file", len(configsData))

	// Parse configurations
	var configs models.RepositoryConfigList
	if err := json.Unmarshal(configsData, &configs); err != nil {
		log.Printf("Failed to parse configurations: %v", err)
		return fmt.Errorf("failed to parse configurations: %w", err)
	}

	log.Printf("Successfully parsed %d repository configurations", len(configs.Configs))

	// Initialize repositories
	for i, config := range configs.Configs {
		log.Printf("Initializing repository %d/%d: %s (%s)", i+1, len(configs.Configs), config.Name, config.ID)
		// Check if repository already exists
		if existingRepo, ok := rm.repositories[config.ID]; ok {
			// Update existing repository
			log.Printf("Updating existing repository: %s", config.ID)
			if err := existingRepo.UpdateConfig(config); err != nil {
				log.Printf("Failed to update repository %s: %v", config.ID, err)
				continue
			}
		} else {
			// Create new repository
			baseDir := filepath.Join(rm.projectRoot, "repos", config.ID)
			// Check if the repository directory already exists
			if _, err := os.Stat(baseDir); err == nil {
				log.Printf("Repository directory already exists: %s, using existing repository", baseDir)
			} else if !os.IsNotExist(err) {
				log.Printf("Error checking repository directory: %v", err)
				continue
			} else {
				log.Printf("Creating new repository in: %s", baseDir)
			}
			repo := NewRepository(config, baseDir, rm.syncLogger, rm)

			// Initialize repository
			if err := repo.InitClient(); err != nil {
				log.Printf("Failed to initialize repository %s: %v", config.ID, err)
				continue
			}

			rm.repositories[config.ID] = repo

			// Start polling if repository is active
			if config.IsActive {
				log.Printf("Starting polling for repository: %s", config.ID)
				if err := repo.StartPolling(); err != nil {
					log.Printf("Failed to start polling for repository %s: %v", config.ID, err)
				}
			}
		}
	}

	log.Printf("Successfully initialized %d repositories", len(rm.repositories))
	return nil
}

// SaveConfigurations saves repository configurations to file
func (rm *RepositoryManager) SaveConfigurations(configs models.RepositoryConfigList) error {
	// Convert configurations to JSON
	configsData, err := json.MarshalIndent(configs, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal configurations: %w", err)
	}

	// Create configurations directory if not exists
	configDir := filepath.Dir(rm.configsPath)
	if _, err := os.Stat(configDir); os.IsNotExist(err) {
		if err := os.MkdirAll(configDir, 0755); err != nil {
			return fmt.Errorf("failed to create configurations directory: %w", err)
		}
	}

	// Write configurations to file
	if err := os.WriteFile(rm.configsPath, configsData, 0644); err != nil {
		return fmt.Errorf("failed to write configurations file: %w", err)
	}

	return nil
}

// GetConfigurations returns the current repository configurations
func (rm *RepositoryManager) GetConfigurations() (models.RepositoryConfigList, error) {
	log.Printf("Getting configurations from: %s", rm.configsPath)

	// Try to load from the root configs directory first
	rootConfigPath := "configs/repositories.json"
	log.Printf("Trying to load from root configs directory: %s", rootConfigPath)

	if _, err := os.Stat(rootConfigPath); err == nil {
		// Root config file exists, use it
		log.Printf("Found configuration file in root directory: %s", rootConfigPath)
		configsData, err := os.ReadFile(rootConfigPath)
		if err != nil {
			log.Printf("Failed to read root configurations file: %v", err)
		} else {
			log.Printf("Successfully read %d bytes from root configuration file", len(configsData))

			// Parse configurations
			var configs models.RepositoryConfigList
			if err := json.Unmarshal(configsData, &configs); err != nil {
				log.Printf("Failed to parse root configurations: %v", err)
			} else {
				log.Printf("Successfully parsed %d repository configurations from root", len(configs.Configs))
				return configs, nil
			}
		}
	} else {
		log.Printf("Root configuration file does not exist: %s, error: %v", rootConfigPath, err)
	}

	// Fall back to the original path if root config loading failed
	// Check if file exists
	if _, err := os.Stat(rm.configsPath); os.IsNotExist(err) {
		log.Printf("Configurations file does not exist: %s", rm.configsPath)
		// Try to load from the absolute path
		absPath, _ := filepath.Abs(rm.configsPath)
		log.Printf("Trying absolute path: %s", absPath)

		if _, err := os.Stat(absPath); os.IsNotExist(err) {
			log.Printf("Absolute path does not exist either")
			return models.RepositoryConfigList{}, fmt.Errorf("configurations file does not exist: %s", rm.configsPath)
		}

		// If absolute path exists, use it
		rm.configsPath = absPath
		log.Printf("Using absolute path for configurations: %s", rm.configsPath)
	}

	// Read configurations from file
	log.Printf("Reading configurations from file: %s", rm.configsPath)
	configsData, err := os.ReadFile(rm.configsPath)
	if err != nil {
		log.Printf("Failed to read configurations file: %v", err)
		return models.RepositoryConfigList{}, fmt.Errorf("failed to read configurations file: %w", err)
	}

	log.Printf("Successfully read %d bytes from configuration file", len(configsData))

	// Parse configurations
	var configs models.RepositoryConfigList
	if err := json.Unmarshal(configsData, &configs); err != nil {
		log.Printf("Failed to parse configurations: %v", err)
		return models.RepositoryConfigList{}, fmt.Errorf("failed to parse configurations: %w", err)
	}

	log.Printf("Successfully parsed %d repository configurations", len(configs.Configs))
	return configs, nil
}

// GetConfiguration returns a specific repository configuration by ID
func (rm *RepositoryManager) GetConfiguration(id string) (models.RepositoryConfig, error) {
	// Get all configurations
	configs, err := rm.GetConfigurations()
	if err != nil {
		return models.RepositoryConfig{}, fmt.Errorf("failed to get configurations: %w", err)
	}

	// Find the configuration with the specified ID
	for _, config := range configs.Configs {
		if config.ID == id {
			return config, nil
		}
	}

	return models.RepositoryConfig{}, fmt.Errorf("repository with ID %s not found", id)
}

// AddConfiguration adds a new repository configuration
func (rm *RepositoryManager) AddConfiguration(config models.RepositoryConfig) error {
	// Generate ID if not provided
	if config.ID == "" {
		config.ID = fmt.Sprintf("repo_%d", len(rm.repositories)+1)
	}

	// Check if repository with this ID already exists
	if _, ok := rm.repositories[config.ID]; ok {
		return fmt.Errorf("repository with ID %s already exists", config.ID)
	}

	// Load current configurations
	configs, err := rm.GetConfigurations()
	if err != nil {
		return fmt.Errorf("failed to get current configurations: %w", err)
	}

	// Add new configuration
	configs.Configs = append(configs.Configs, config)

	// Save updated configurations
	if err := rm.SaveConfigurations(configs); err != nil {
		return fmt.Errorf("failed to save updated configurations: %w", err)
	}

	// Initialize repository
	baseDir := filepath.Join(rm.projectRoot, "repos", config.ID)
	// Check if the repository directory already exists
	if _, err := os.Stat(baseDir); err == nil {
		log.Printf("Repository directory already exists: %s, using existing repository", baseDir)
	} else if !os.IsNotExist(err) {
		log.Printf("Error checking repository directory: %v", err)
		return fmt.Errorf("error checking repository directory: %w", err)
	} else {
		log.Printf("Creating new repository in: %s", baseDir)
	}
	repo := NewRepository(config, baseDir, rm.syncLogger, rm)

	// Initialize repository client
	if err := repo.InitClient(); err != nil {
		return fmt.Errorf("failed to initialize repository client: %w", err)
	}

	rm.repositories[config.ID] = repo

	// Start polling if repository is active
	if config.IsActive {
		if err := repo.StartPolling(); err != nil {
			return fmt.Errorf("failed to start polling for repository: %w", err)
		}
	}

	return nil
}

// UpdateConfiguration updates an existing repository configuration
func (rm *RepositoryManager) UpdateConfiguration(config models.RepositoryConfig) error {
	// Check if repository with this ID exists
	if _, ok := rm.repositories[config.ID]; !ok {
		return fmt.Errorf("repository with ID %s not found", config.ID)
	}

	// Load current configurations
	configs, err := rm.GetConfigurations()
	if err != nil {
		return fmt.Errorf("failed to get current configurations: %w", err)
	}

	// Update configuration
	found := false
	for i, existingConfig := range configs.Configs {
		if existingConfig.ID == config.ID {
			configs.Configs[i] = config
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("repository with ID %s not found in configurations", config.ID)
	}

	// Save updated configurations
	if err := rm.SaveConfigurations(configs); err != nil {
		return fmt.Errorf("failed to save updated configurations: %w", err)
	}

	// Update repository
	repo, ok := rm.repositories[config.ID]
	if !ok {
		// Create new repository
		baseDir := filepath.Join(rm.projectRoot, "repos", config.ID)
		// Check if the repository directory already exists
		if _, err := os.Stat(baseDir); err == nil {
			log.Printf("Repository directory already exists: %s, using existing repository", baseDir)
		} else if !os.IsNotExist(err) {
			log.Printf("Error checking repository directory: %v", err)
			return fmt.Errorf("error checking repository directory: %w", err)
		} else {
			log.Printf("Creating new repository in: %s", baseDir)
		}
		repo = NewRepository(config, baseDir, rm.syncLogger, rm)

		// Initialize repository
		if err := repo.InitClient(); err != nil {
			return fmt.Errorf("failed to initialize repository: %w", err)
		}

		rm.repositories[config.ID] = repo
	} else {
		// Update existing repository
		if err := repo.UpdateConfig(config); err != nil {
			return fmt.Errorf("failed to update repository configuration: %w", err)
		}
	}

	// Start or stop polling based on IsActive flag
	if config.IsActive {
		if err := repo.StartPolling(); err != nil {
			return fmt.Errorf("failed to start polling for repository: %w", err)
		}
	} else {
		repo.StopPolling()
	}

	return nil
}

// DeleteConfiguration deletes a repository configuration
func (rm *RepositoryManager) DeleteConfiguration(id string) error {
	// Check if repository with this ID exists
	if repo, ok := rm.repositories[id]; ok {
		// Stop polling
		repo.StopPolling()
	}

	// Load current configurations
	configs, err := rm.GetConfigurations()
	if err != nil {
		return fmt.Errorf("failed to get current configurations: %w", err)
	}

	// Remove configuration
	found := false
	newConfigs := []models.RepositoryConfig{}
	for _, cfg := range configs.Configs {
		if cfg.ID != id {
			newConfigs = append(newConfigs, cfg)
		} else {
			found = true
		}
	}

	if !found {
		return fmt.Errorf("repository configuration with ID %s not found", id)
	}

	configs.Configs = newConfigs

	// Save updated configurations
	if err := rm.SaveConfigurations(configs); err != nil {
		return fmt.Errorf("failed to save updated configurations: %w", err)
	}

	// Remove repository from manager
	delete(rm.repositories, id)

	return nil
}

// GetRepository returns a repository by ID
func (rm *RepositoryManager) GetRepository(id string) (Repository, error) {
	if repo, ok := rm.repositories[id]; ok {
		return repo, nil
	}
	return nil, fmt.Errorf("repository with ID %s not found", id)
}

// GetRepositoryStatus returns the status of a repository
func (rm *RepositoryManager) GetRepositoryStatus(id string) (models.RepositoryStatus, error) {
	if repo, ok := rm.repositories[id]; ok {
		return repo.GetRepositoryStatus(), nil
	}
	return models.RepositoryStatus{}, fmt.Errorf("repository with ID %s not found", id)
}

// GetAllRepositoryStatuses returns the status of all repositories
func (rm *RepositoryManager) GetAllRepositoryStatuses() map[string]models.RepositoryStatus {
	statuses := make(map[string]models.RepositoryStatus)
	for id, repo := range rm.repositories {
		statuses[id] = repo.GetRepositoryStatus()
	}
	return statuses
}

// RegisterChangeCallback registers a callback for a specific repository
func (rm *RepositoryManager) RegisterChangeCallback(repoID string, callback func()) error {
	if repo, ok := rm.repositories[repoID]; ok {
		repo.RegisterChangeCallback(callback)
		return nil
	}
	return fmt.Errorf("repository with ID %s not found", repoID)
}

// GetRepositories returns all repositories
func (rm *RepositoryManager) GetRepositories() ([]Repository, error) {
	repositories := make([]Repository, 0, len(rm.repositories))
	for _, repo := range rm.repositories {
		repositories = append(repositories, repo)
	}
	return repositories, nil
}

// TriggerReindexing triggers reindexing for a specific repository
func (rm *RepositoryManager) TriggerReindexing(repoID string) error {
	// Check if search controller is set
	if rm.searchController == nil {
		log.Printf("Warning: Search controller not set, skipping reindexing for repository %s", repoID)
		return nil
	}

	// Trigger reindexing
	log.Printf("Triggering reindexing for repository %s", repoID)
	if err := rm.searchController.ReindexRepository(repoID); err != nil {
		log.Printf("Error reindexing repository %s: %v", repoID, err)
		return fmt.Errorf("failed to reindex repository: %w", err)
	}

	log.Printf("Successfully reindexed repository %s", repoID)
	return nil
}

// GetRepoInstances returns all repositories as RepoInstance interface
func (rm *RepositoryManager) GetRepoInstances() ([]services.RepoInstance, error) {
	repositories := make([]services.RepoInstance, 0, len(rm.repositories))
	for _, repo := range rm.repositories {
		repositories = append(repositories, repo)
	}
	return repositories, nil
}

// For backward compatibility
type GitLabManager = RepositoryManager

// NewGitLabManager for backward compatibility
func NewGitLabManager(configsPath string, syncLogger *services.SyncLogger) *RepositoryManager {
	return NewRepositoryManager(configsPath, syncLogger)
}
