package repository

import (
	"archive/zip"
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"
	"adgitops-ui/src/backend/services"

	"github.com/xanzy/go-gitlab"
)

// GitLabRepository handles the GitLab integration and repository monitoring
type GitLabRepository struct {
	config         models.GitLabConfig
	localRepoDir   string
	lastCommit     string
	isPolling      bool
	syncInProgress bool       // Flag to track manual sync operations
	syncMutex      sync.Mutex // Mutex to protect syncInProgress flag
	callbacks      []func()
	client         *gitlab.Client
	syncLogger     *services.SyncLogger
	repoID         string
}

// NewGitLabRepository creates a new GitLab repository instance
func NewGitLabRepository(config models.GitLabConfig, localRepoDir string, syncLogger *services.SyncLogger) *GitLabRepository {
	return &GitLabRepository{
		config:       config,
		localRepoDir: localRepoDir,
		callbacks:    make([]func(), 0),
		syncLogger:   syncLogger,
		repoID:       config.ID,
	}
}

// InitClient initializes the GitLab client
func (gr *GitLabRepository) InitClient() error {
	if gr.config.URL == "" || gr.config.Token == "" {
		return fmt.Errorf("GitLab URL and token are required")
	}

	client, err := gitlab.NewClient(gr.config.Token, gitlab.WithBaseURL(gr.config.URL))
	if err != nil {
		return fmt.Errorf("failed to create GitLab client: %w", err)
	}

	gr.client = client
	return nil
}

// StartPolling begins periodic polling for repository changes
func (gr *GitLabRepository) StartPolling() error {
	if gr.isPolling {
		return nil // Already polling
	}

	// Ensure the repository exists locally
	if err := gr.EnsureLocalRepoExists(); err != nil {
		return fmt.Errorf("failed to ensure local repository exists: %w", err)
	}

	gr.isPolling = true
	go gr.pollRepository()
	return nil
}

// StopPolling stops the periodic polling
func (gr *GitLabRepository) StopPolling() {
	gr.isPolling = false
}

// RegisterChangeCallback registers a function to be called when repository changes are detected
func (gr *GitLabRepository) RegisterChangeCallback(callback func()) {
	gr.callbacks = append(gr.callbacks, callback)
}

// pollRepository periodically checks for changes in the repository
func (gr *GitLabRepository) pollRepository() {
	ticker := time.NewTicker(time.Duration(gr.config.PollFrequency) * time.Second)
	defer ticker.Stop()

	for {
		if !gr.isPolling {
			return
		}

		hasChanges, err := gr.checkForChanges()
		if err != nil {
			gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Error checking for changes: %v", err), models.LogLevelError)
		} else if hasChanges {
			// Call all registered callbacks
			for _, callback := range gr.callbacks {
				callback()
			}
		}

		select {
		case <-ticker.C:
			// Continue polling
		}
	}
}

// checkForChanges checks if the remote repository has changes
func (gr *GitLabRepository) checkForChanges() (bool, error) {
	if gr.client == nil {
		return false, fmt.Errorf("GitLab client not initialized")
	}

	// Log the poll frequency for debugging
	gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Repository poll frequency: %d seconds", gr.config.PollFrequency), models.LogLevelInfo)

	// Fetch commits from the GitLab API
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get the most recent commit
	opts := &gitlab.ListCommitsOptions{
		RefName: gitlab.String("main"), // Default to main branch
		ListOptions: gitlab.ListOptions{
			PerPage: 1,
		},
	}

	gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Fetching latest commit for project ID: %s", gr.config.ProjectID), models.LogLevelInfo)

	commits, _, err := gr.client.Commits.ListCommits(gr.config.ProjectID, opts, gitlab.WithContext(ctx))
	if err != nil {
		// Try with master branch if main fails
		gr.syncLogger.AddLog(gr.repoID, "Failed to get commits from main branch, trying master branch", models.LogLevelWarning)
		opts.RefName = gitlab.String("master")
		commits, _, err = gr.client.Commits.ListCommits(gr.config.ProjectID, opts, gitlab.WithContext(ctx))
		if err != nil {
			gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Failed to list commits: %v", err), models.LogLevelError)
			return false, fmt.Errorf("failed to list commits: %w", err)
		}
	}

	if len(commits) == 0 {
		gr.syncLogger.AddLog(gr.repoID, "No commits found in repository", models.LogLevelWarning)
		return false, nil // No commits found
	}

	latestCommit := commits[0].ID
	gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Latest remote commit: %s", latestCommit), models.LogLevelInfo)

	// Read the current commit hash from file if in-memory value is empty
	if gr.lastCommit == "" {
		commitFile := filepath.Join(gr.GetLocalRepoPath(), ".git", "COMMIT_HASH")
		if data, err := os.ReadFile(commitFile); err == nil {
			gr.lastCommit = string(data)
			gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Loaded commit hash from file: %s", gr.lastCommit), models.LogLevelInfo)
		} else {
			gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Failed to read commit hash file: %v", err), models.LogLevelWarning)
		}
	}

	// Log the current in-memory commit hash
	gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Current in-memory commit hash: %s", gr.lastCommit), models.LogLevelInfo)

	// Determine if there are changes
	hasChanges := gr.lastCommit != latestCommit && gr.lastCommit != ""

	// Log detailed comparison
	gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Commit comparison - Local: %s, Remote: %s", gr.lastCommit, latestCommit), models.LogLevelInfo)

	// Update the in-memory last commit
	gr.lastCommit = latestCommit

	if hasChanges {
		gr.syncLogger.AddLog(gr.repoID, "Repository has changes available", models.LogLevelInfo)

		// Log the number of registered callbacks
		gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Number of registered callbacks: %d", len(gr.callbacks)), models.LogLevelInfo)

		// Check if we have any callbacks registered
		if len(gr.callbacks) == 0 {
			gr.syncLogger.AddLog(gr.repoID, "WARNING: No callbacks registered for repository changes", models.LogLevelWarning)
		}
	} else {
		gr.syncLogger.AddLog(gr.repoID, "Repository is up to date", models.LogLevelInfo)
	}

	return hasChanges, nil
}

// SyncRepository downloads the latest repository files using the GitLab API
func (gr *GitLabRepository) SyncRepository() error {
	// Use mutex to safely check and set syncInProgress flag
	gr.syncMutex.Lock()
	if gr.syncInProgress {
		gr.syncMutex.Unlock()
		gr.syncLogger.AddLog(gr.repoID, "Repository is already being synchronized, please wait for the current operation to complete", models.LogLevelWarning)
		return fmt.Errorf("repository is already being synchronized")
	}

	// Set syncInProgress flag to true
	gr.syncInProgress = true
	gr.syncMutex.Unlock()

	// Make sure to reset the flag when we're done
	defer func() {
		gr.syncMutex.Lock()
		gr.syncInProgress = false
		gr.syncMutex.Unlock()
	}()

	// Ensure local repository exists
	if err := gr.EnsureLocalRepoExists(); err != nil {
		gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Failed to ensure local repository exists: %v", err), models.LogLevelError)
		return fmt.Errorf("failed to ensure local repository exists: %w", err)
	}

	gr.syncLogger.AddLog(gr.repoID, "Starting repository sync using GitLab API", models.LogLevelInfo)

	// Get latest commit from GitLab
	commit, _, err := gr.client.Commits.GetCommit(gr.config.ProjectID, "main", nil)
	if err != nil {
		// Try with master branch if main fails
		commit, _, err = gr.client.Commits.GetCommit(gr.config.ProjectID, "master", nil)
		if err != nil {
			gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Failed to get latest commit: %v", err), models.LogLevelError)
			return fmt.Errorf("failed to get latest commit: %w", err)
		}
	}

	// Check if we need to sync
	latestCommitID := commit.ID

	// Force sync for testing - always sync regardless of commit hash
	gr.syncLogger.AddLog(gr.repoID, "Forcing repository sync for testing", models.LogLevelInfo)

	// Uncomment this block to restore normal behavior
	/*
		if gr.lastCommit == latestCommitID && gr.lastCommit != "" {
			gr.syncLogger.AddLog(gr.repoID, "Repository already up to date", models.LogLevelSuccess)
			return nil
		}
	*/

	// Download repository archive
	gr.syncLogger.AddLog(gr.repoID, "Downloading repository archive...", models.LogLevelInfo)
	if err := gr.downloadRepositoryArchive(latestCommitID); err != nil {
		gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Failed to download repository archive: %v", err), models.LogLevelError)
		return fmt.Errorf("failed to download repository archive: %w", err)
	}

	// Update last commit
	gr.lastCommit = latestCommitID
	gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Synced repository to commit %s", gr.lastCommit), models.LogLevelSuccess)

	return nil
}

// downloadRepositoryArchive downloads the repository archive from GitLab and extracts it
func (gr *GitLabRepository) downloadRepositoryArchive(commitID string) error {
	// Path to download the repository contents
	localPath := gr.GetLocalRepoPath()

	// Create an empty .git directory to simulate a git repo (for compatibility)
	gitDir := filepath.Join(localPath, ".git")
	if err := os.MkdirAll(gitDir, 0755); err != nil {
		return fmt.Errorf("failed to create .git directory: %w", err)
	}

	// Get repository archive from GitLab API
	opt := &gitlab.ArchiveOptions{
		SHA: gitlab.String(commitID),
	}

	// Create a temporary file to store the archive
	tempFile, err := os.CreateTemp("", "gitlab-repo-*.zip")
	if err != nil {
		return fmt.Errorf("failed to create temporary file: %w", err)
	}
	defer os.Remove(tempFile.Name())

	// Download the archive
	archive, _, err := gr.client.Repositories.Archive(gr.config.ProjectID, opt)
	if err != nil {
		return fmt.Errorf("failed to get repository archive: %w", err)
	}

	// Save archive to temporary file
	_, err = tempFile.Write(archive)
	if err != nil {
		return fmt.Errorf("failed to save archive: %w", err)
	}
	tempFile.Close()

	// Clear the repository directory (except .git)
	entries, err := os.ReadDir(localPath)
	if err != nil {
		return fmt.Errorf("failed to read directory: %w", err)
	}

	for _, entry := range entries {
		if entry.Name() != ".git" {
			entryPath := filepath.Join(localPath, entry.Name())
			if err := os.RemoveAll(entryPath); err != nil {
				return fmt.Errorf("failed to remove %s: %w", entryPath, err)
			}
		}
	}

	// Extract archive
	gr.syncLogger.AddLog(gr.repoID, "Extracting repository files...", models.LogLevelInfo)
	if err := gr.extractZip(tempFile.Name(), localPath); err != nil {
		return fmt.Errorf("failed to extract archive: %w", err)
	}

	// Write the current commit hash to a file in the .git directory
	commitFile := filepath.Join(gitDir, "COMMIT_HASH")
	if err := os.WriteFile(commitFile, []byte(commitID), 0644); err != nil {
		return fmt.Errorf("failed to write commit hash: %w", err)
	}

	return nil
}

// extractZip extracts a zip archive to the specified destination
func (gr *GitLabRepository) extractZip(zipFile, destination string) error {
	reader, err := zip.OpenReader(zipFile)
	if err != nil {
		return fmt.Errorf("failed to open zip file: %w", err)
	}
	defer reader.Close()

	// Detect if archive has a wrapper directory by checking if all files start with the same prefix
	var commonPrefix string
	hasWrapperDir := true

	if len(reader.File) > 0 {
		// Get the first path component of the first file
		firstFile := reader.File[0].Name
		if idx := strings.Index(firstFile, "/"); idx != -1 {
			commonPrefix = firstFile[:idx+1]

			// Check if all files start with this prefix
			for _, file := range reader.File {
				if !strings.HasPrefix(file.Name, commonPrefix) {
					hasWrapperDir = false
					break
				}
			}
		} else {
			hasWrapperDir = false
		}
	}

	// Log the detected structure for debugging
	if hasWrapperDir {
		gr.syncLogger.AddLog(gr.repoID, fmt.Sprintf("Detected wrapper directory with prefix: %s", commonPrefix), models.LogLevelInfo)
	} else {
		gr.syncLogger.AddLog(gr.repoID, "No wrapper directory detected in archive", models.LogLevelInfo)
	}

	// Create any subdirectories if they don't exist
	for _, file := range reader.File {
		var destPath string

		if hasWrapperDir && commonPrefix != "" {
			// Remove the wrapper directory prefix to preserve exact repository structure
			relativePath := strings.TrimPrefix(file.Name, commonPrefix)
			if relativePath == "" {
				continue // Skip the wrapper directory itself
			}
			destPath = filepath.Join(destination, relativePath)
		} else {
			// No wrapper directory, preserve the full path
			destPath = filepath.Join(destination, file.Name)
		}

		if file.FileInfo().IsDir() {
			if err := os.MkdirAll(destPath, 0755); err != nil {
				return fmt.Errorf("failed to create directory %s: %w", destPath, err)
			}
			continue
		}

		// Make sure the directory exists
		if err := os.MkdirAll(filepath.Dir(destPath), 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %w", filepath.Dir(destPath), err)
		}

		// Extract file
		srcFile, err := file.Open()
		if err != nil {
			return fmt.Errorf("failed to open file in zip: %w", err)
		}

		destFile, err := os.Create(destPath)
		if err != nil {
			srcFile.Close()
			return fmt.Errorf("failed to create destination file: %w", err)
		}

		_, err = io.Copy(destFile, srcFile)
		srcFile.Close()
		destFile.Close()

		if err != nil {
			return fmt.Errorf("failed to copy file contents: %w", err)
		}
	}

	return nil
}

// GetRepositoryStatus returns the current status of the repository
func (gr *GitLabRepository) GetRepositoryStatus() models.RepositoryStatus {
	// Safely check syncInProgress flag
	gr.syncMutex.Lock()
	syncInProgress := gr.syncInProgress
	gr.syncMutex.Unlock()

	// Read the last sync time from file
	lastSync := time.Now().Format(time.RFC3339)
	syncTimeFile := filepath.Join(gr.GetLocalRepoPath(), "LAST_SYNC")
	if data, err := os.ReadFile(syncTimeFile); err == nil {
		lastSync = string(data)
	}

	// Read the last commit from file if not in memory
	lastCommit := gr.lastCommit
	if lastCommit == "" {
		commitFile := filepath.Join(gr.GetLocalRepoPath(), "COMMIT_HASH")
		if data, err := os.ReadFile(commitFile); err == nil {
			lastCommit = string(data)
		}
	}

	return models.RepositoryStatus{
		LastSync:       lastSync,
		LastCommit:     lastCommit,
		HasChanges:     false,          // This will be updated during the next poll
		SyncInProgress: syncInProgress, // Use the actual sync status
	}
}

// EnsureLocalRepoExists makes sure the local repository directory exists
func (gr *GitLabRepository) EnsureLocalRepoExists() error {
	repoPath := gr.GetLocalRepoPath()
	if _, err := os.Stat(repoPath); os.IsNotExist(err) {
		if err := os.MkdirAll(repoPath, 0755); err != nil {
			return fmt.Errorf("failed to create repository directory: %w", err)
		}
	}
	return nil
}

// GetLocalRepoPath returns the path to the local repository
func (gr *GitLabRepository) GetLocalRepoPath() string {
	// Just return the localRepoDir directly, which already includes the config.ID
	return gr.localRepoDir
}

// UpdateConfig updates the GitLab configuration
func (gr *GitLabRepository) UpdateConfig(config models.RepositoryConfig) error {
	// Convert RepositoryConfig to GitLabConfig
	glConfig := models.GitLabConfig(config)

	// Stop polling with the old configuration
	wasPolling := gr.isPolling
	if wasPolling {
		gr.StopPolling()
	}

	// Update the configuration
	gr.config = glConfig

	// Re-initialize the client with the new configuration
	if err := gr.InitClient(); err != nil {
		return fmt.Errorf("failed to initialize GitLab client with new configuration: %w", err)
	}

	// Restart polling if it was active before
	if wasPolling {
		if err := gr.StartPolling(); err != nil {
			return fmt.Errorf("failed to restart polling with new configuration: %w", err)
		}
	}

	return nil
}

// GetConfig returns the current repository configuration
func (gr *GitLabRepository) GetConfig() models.RepositoryConfig {
	// Convert GitLabConfig to RepositoryConfig
	return models.RepositoryConfig(gr.config)
}

// GetID returns the repository ID
func (gr *GitLabRepository) GetID() string {
	return gr.repoID
}
